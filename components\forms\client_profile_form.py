"""
Client Profile Form
===================

Form component for client profile data entry.
"""

import flet as ft
from typing import Optional, Callable, Any, Dict

from models.client_profile import ClientProfile


class ClientProfileForm:
    """Form component for client profile data."""
    
    def __init__(self, client_profile: ClientProfile):
        self.client_profile = client_profile
        self.on_data_changed: Optional[Callable[[str, Any], None]] = None
        
        # Form fields
        self.company_name_field: Optional[ft.TextField] = None
        self.client_name_field: Optional[ft.TextField] = None
        self.contact_email_field: Optional[ft.TextField] = None
        self.phone_field: Optional[ft.TextField] = None
        self.project_name_field: Optional[ft.TextField] = None
        self.project_location_field: Optional[ft.TextField] = None
        self.project_capacity_field: Optional[ft.TextField] = None
        self.preferred_currency_dropdown: Optional[ft.Dropdown] = None
    
    def build(self) -> ft.Container:
        """Build the client profile form."""
        
        # Company information section
        company_section = ft.Column([
            ft.Text("Company Information", size=16, weight=ft.FontWeight.BOLD),
            ft.Divider(height=10),
            
            self._create_company_name_field(),
            self._create_client_name_field(),
            self._create_contact_email_field(),
            self._create_phone_field()
        ])
        
        # Project information section
        project_section = ft.Column([
            ft.Text("Project Information", size=16, weight=ft.FontWeight.BOLD),
            ft.Divider(height=10),
            
            self._create_project_name_field(),
            self._create_project_location_field(),
            ft.Row([
                self._create_project_capacity_field(),
                self._create_currency_dropdown()
            ])
        ])
        
        return ft.Container(
            content=ft.Column([
                company_section,
                ft.Divider(height=20),
                project_section
            ]),
            padding=15
        )
    
    def _create_company_name_field(self) -> ft.TextField:
        """Create company name field."""
        self.company_name_field = ft.TextField(
            label="Company Name *",
            value=self.client_profile.company_name,
            hint_text="Enter company name",
            on_change=lambda e: self._on_field_changed('company_name', e.control.value),
            expand=True
        )
        return self.company_name_field
    
    def _create_client_name_field(self) -> ft.TextField:
        """Create client name field."""
        self.client_name_field = ft.TextField(
            label="Client Name *",
            value=self.client_profile.client_name,
            hint_text="Enter client contact name",
            on_change=lambda e: self._on_field_changed('client_name', e.control.value),
            expand=True
        )
        return self.client_name_field
    
    def _create_contact_email_field(self) -> ft.TextField:
        """Create contact email field."""
        self.contact_email_field = ft.TextField(
            label="Contact Email",
            value=self.client_profile.contact_email,
            hint_text="Enter email address",
            keyboard_type=ft.KeyboardType.EMAIL,
            on_change=lambda e: self._on_field_changed('contact_email', e.control.value),
            expand=True
        )
        return self.contact_email_field
    
    def _create_phone_field(self) -> ft.TextField:
        """Create phone field."""
        self.phone_field = ft.TextField(
            label="Phone Number",
            value=self.client_profile.phone,
            hint_text="Enter phone number",
            keyboard_type=ft.KeyboardType.PHONE,
            on_change=lambda e: self._on_field_changed('phone', e.control.value),
            expand=True
        )
        return self.phone_field
    
    def _create_project_name_field(self) -> ft.TextField:
        """Create project name field."""
        self.project_name_field = ft.TextField(
            label="Project Name *",
            value=self.client_profile.project_name,
            hint_text="Enter project name",
            on_change=lambda e: self._on_field_changed('project_name', e.control.value),
            expand=True
        )
        return self.project_name_field
    
    def _create_project_location_field(self) -> ft.TextField:
        """Create project location field."""
        self.project_location_field = ft.TextField(
            label="Project Location",
            value=self.client_profile.project_location,
            hint_text="Enter project location",
            on_change=lambda e: self._on_field_changed('project_location', e.control.value),
            expand=True
        )
        return self.project_location_field
    
    def _create_project_capacity_field(self) -> ft.TextField:
        """Create project capacity field."""
        self.project_capacity_field = ft.TextField(
            label="Project Capacity (MW)",
            value=str(self.client_profile.project_capacity_mw) if self.client_profile.project_capacity_mw else "",
            hint_text="Enter capacity in MW",
            keyboard_type=ft.KeyboardType.NUMBER,
            on_change=self._on_capacity_changed,
            expand=True
        )
        return self.project_capacity_field
    
    def _create_currency_dropdown(self) -> ft.Dropdown:
        """Create currency dropdown."""
        self.preferred_currency_dropdown = ft.Dropdown(
            label="Preferred Currency",
            value=self.client_profile.preferred_currency,
            options=[
                ft.dropdown.Option("EUR", "EUR - Euro"),
                ft.dropdown.Option("USD", "USD - US Dollar"),
                ft.dropdown.Option("MAD", "MAD - Moroccan Dirham"),
                ft.dropdown.Option("GBP", "GBP - British Pound")
            ],
            on_change=lambda e: self._on_field_changed('preferred_currency', e.control.value),
            expand=True
        )
        return self.preferred_currency_dropdown
    
    def _on_field_changed(self, field_name: str, value: str):
        """Handle field value changes."""
        if self.on_data_changed:
            self.on_data_changed(field_name, value)
    
    def _on_capacity_changed(self, e):
        """Handle capacity field changes with validation."""
        try:
            value = float(e.control.value) if e.control.value else None
            if self.on_data_changed:
                self.on_data_changed('project_capacity_mw', value)
        except ValueError:
            # Invalid number, ignore
            pass
    
    def update_data(self, client_profile: ClientProfile):
        """Update form with new client profile data."""
        self.client_profile = client_profile
        
        if self.company_name_field:
            self.company_name_field.value = client_profile.company_name
        if self.client_name_field:
            self.client_name_field.value = client_profile.client_name
        if self.contact_email_field:
            self.contact_email_field.value = client_profile.contact_email
        if self.phone_field:
            self.phone_field.value = client_profile.phone
        if self.project_name_field:
            self.project_name_field.value = client_profile.project_name
        if self.project_location_field:
            self.project_location_field.value = client_profile.project_location
        if self.project_capacity_field:
            self.project_capacity_field.value = str(client_profile.project_capacity_mw) if client_profile.project_capacity_mw else ""
        if self.preferred_currency_dropdown:
            self.preferred_currency_dropdown.value = client_profile.preferred_currency
    
    def validate(self) -> Dict[str, str]:
        """Validate form data."""
        return self.client_profile.validate()
    
    def get_data(self) -> ClientProfile:
        """Get current form data as ClientProfile."""
        return self.client_profile
