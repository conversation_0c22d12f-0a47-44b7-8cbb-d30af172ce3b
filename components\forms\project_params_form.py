"""
Project Parameters Form
=======================

Form component for project parameters data entry.
"""

import flet as ft
from typing import Optional, Callable, Any, Dict

from models.project_assumptions import EnhancedProjectAssumptions


class ProjectParamsForm:
    """Form component for project parameters."""
    
    def __init__(self, project_assumptions: EnhancedProjectAssumptions):
        self.project_assumptions = project_assumptions
        self.on_data_changed: Optional[Callable[[str, Any], None]] = None
        
        # Form fields - will be created in build()
        self.fields: Dict[str, ft.TextField] = {}
    
    def build(self) -> ft.Container:
        """Build the project parameters form."""
        
        # Technical parameters section
        technical_section = self._create_technical_section()
        
        # Financial parameters section
        financial_section = self._create_financial_section()
        
        # Grant parameters section
        grant_section = self._create_grant_section()
        
        return ft.Container(
            content=ft.Column([
                technical_section,
                ft.Divider(height=20),
                financial_section,
                ft.Divider(height=20),
                grant_section
            ]),
            padding=15
        )
    
    def _create_technical_section(self) -> ft.Column:
        """Create technical parameters section."""
        return ft.Column([
            ft.Text("Technical Parameters", size=16, weight=ft.FontWeight.BOLD),
            ft.Divider(height=10),
            
            ft.Row([
                self._create_field("capacity_mw", "Capacity (MW)", "10.0"),
                self._create_field("project_life_years", "Project Life (years)", "25")
            ]),
            
            ft.Row([
                self._create_field("production_mwh_year1", "Production Year 1 (MWh)", "18000"),
                self._create_field("degradation_rate", "Degradation Rate (%)", "0.5")
            ]),
            
            ft.Row([
                self._create_field("capex_meur", "CAPEX (M EUR)", "8.5"),
                self._create_field("opex_keuros_year1", "OPEX Year 1 (k EUR)", "180")
            ])
        ])
    
    def _create_financial_section(self) -> ft.Column:
        """Create financial parameters section."""
        return ft.Column([
            ft.Text("Financial Parameters", size=16, weight=ft.FontWeight.BOLD),
            ft.Divider(height=10),
            
            ft.Row([
                self._create_field("ppa_price_eur_kwh", "PPA Price (EUR/kWh)", "0.045"),
                self._create_field("ppa_escalation", "PPA Escalation (%)", "0.0")
            ]),
            
            ft.Row([
                self._create_field("debt_ratio", "Debt Ratio", "0.75"),
                self._create_field("interest_rate", "Interest Rate (%)", "6.0")
            ]),
            
            ft.Row([
                self._create_field("debt_years", "Debt Tenor (years)", "15"),
                self._create_field("discount_rate", "Discount Rate (%)", "8.0")
            ]),
            
            ft.Row([
                self._create_field("tax_rate", "Tax Rate (%)", "30.0"),
                self._create_field("land_lease_eur_mw_year", "Land Lease (EUR/MW/year)", "2000")
            ])
        ])
    
    def _create_grant_section(self) -> ft.Column:
        """Create grant parameters section."""
        return ft.Column([
            ft.Text("Grant Parameters", size=16, weight=ft.FontWeight.BOLD),
            ft.Divider(height=10),
            
            ft.Row([
                self._create_field("grant_meur_italy", "Italian Grant (M EUR)", "0.0"),
                self._create_field("grant_meur_masen", "MASEN Grant (M EUR)", "0.0")
            ]),
            
            ft.Row([
                self._create_field("grant_meur_connection", "Connection Grant (M EUR)", "0.0"),
                self._create_field("grant_meur_simest_africa", "SIMEST African Fund (M EUR)", "0.0")
            ])
        ])
    
    def _create_field(self, field_name: str, label: str, default_value: str) -> ft.TextField:
        """Create a form field."""
        current_value = getattr(self.project_assumptions, field_name, default_value)
        
        field = ft.TextField(
            label=label,
            value=str(current_value),
            hint_text=f"Enter {label.lower()}",
            keyboard_type=ft.KeyboardType.NUMBER,
            on_change=lambda e, name=field_name: self._on_field_changed(name, e.control.value),
            expand=True
        )
        
        self.fields[field_name] = field
        return field
    
    def _on_field_changed(self, field_name: str, value: str):
        """Handle field value changes."""
        try:
            # Convert to appropriate type
            if field_name in ['capacity_mw', 'production_mwh_year1', 'capex_meur', 'opex_keuros_year1',
                             'ppa_price_eur_kwh', 'debt_ratio', 'interest_rate', 'discount_rate',
                             'tax_rate', 'degradation_rate', 'ppa_escalation', 'land_lease_eur_mw_year',
                             'grant_meur_italy', 'grant_meur_masen', 'grant_meur_connection', 'grant_meur_simest_africa']:
                numeric_value = float(value) if value else 0.0
                
                # Convert percentages to decimals
                if field_name in ['degradation_rate', 'ppa_escalation', 'interest_rate', 'discount_rate', 'tax_rate']:
                    numeric_value = numeric_value / 100.0
                
                if self.on_data_changed:
                    self.on_data_changed(field_name, numeric_value)
            
            elif field_name in ['project_life_years', 'debt_years']:
                int_value = int(float(value)) if value else 0
                if self.on_data_changed:
                    self.on_data_changed(field_name, int_value)
            
            else:
                if self.on_data_changed:
                    self.on_data_changed(field_name, value)
        
        except ValueError:
            # Invalid number, ignore
            pass
    
    def update_data(self, project_assumptions: EnhancedProjectAssumptions):
        """Update form with new project assumptions data."""
        self.project_assumptions = project_assumptions
        
        # Update all fields
        for field_name, field in self.fields.items():
            value = getattr(project_assumptions, field_name, 0)
            
            # Convert decimals to percentages for display
            if field_name in ['degradation_rate', 'ppa_escalation', 'interest_rate', 'discount_rate', 'tax_rate']:
                value = value * 100.0
            
            field.value = str(value)
    
    def validate(self) -> Dict[str, str]:
        """Validate form data."""
        return self.project_assumptions.validate_all()
    
    def get_data(self) -> EnhancedProjectAssumptions:
        """Get current form data as EnhancedProjectAssumptions."""
        return self.project_assumptions
